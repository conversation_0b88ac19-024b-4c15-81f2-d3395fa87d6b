<?php
/**
 * 简单的微信菜单查询工具
 * 独立运行，不依赖框架
 */

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'zcgk',
    'charset' => 'utf8'
];

// 微信服务号ID
$service_id = isset($_GET['id']) ? intval($_GET['id']) : 1;

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}", 
        $db_config['username'], 
        $db_config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取服务号配置
    $stmt = $pdo->prepare("SELECT name, appid, secret, menu_content FROM z_service WHERE id = ?");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$service) {
        throw new Exception("服务号ID {$service_id} 不存在");
    }
    
    echo "=== 微信服务号菜单查询工具 ===\n";
    echo "服务号：{$service['name']}\n";
    echo "AppID: {$service['appid']}\n\n";
    
    // 显示数据库中的菜单配置
    echo "📋 数据库中的菜单配置：\n";
    $db_menu = json_decode($service['menu_content'], true);
    echo json_encode($db_menu, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
    
    // 提取数据库中的URL
    $db_urls = extractUrls($db_menu);
    echo "数据库中的URL：\n";
    foreach ($db_urls as $name => $url) {
        $status = (strpos($url, 'zhongcaiguoke.cn') !== false) ? '✅' : '❌';
        echo "  {$status} {$name}: {$url}\n";
    }
    echo "\n";
    
    // 尝试获取微信服务器上的菜单（可能会因为IP白名单失败）
    echo "🔍 尝试获取微信服务器上的菜单...\n";
    
    $access_token = getAccessToken($service['appid'], $service['secret']);
    
    if ($access_token) {
        echo "✅ Access Token获取成功\n";
        
        $wechat_menu = getWechatMenu($access_token);
        
        if ($wechat_menu) {
            echo "\n微信服务器上的菜单配置：\n";
            echo json_encode($wechat_menu, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
            
            $wechat_urls = extractUrls($wechat_menu);
            echo "微信服务器上的URL：\n";
            foreach ($wechat_urls as $name => $url) {
                $status = (strpos($url, 'zhongcaiguoke.cn') !== false) ? '✅' : '❌';
                echo "  {$status} {$name}: {$url}\n";
            }
            
            // 比较结果
            echo "\n🔄 对比结果：\n";
            if (json_encode($db_urls) === json_encode($wechat_urls)) {
                echo "✅ 数据库和微信服务器菜单一致\n";
            } else {
                echo "❌ 数据库和微信服务器菜单不一致，需要重新推送菜单\n";
                echo "推送菜单URL: https://你的域名/Prime/Service/taskmenu?id={$service_id}\n";
            }
        }
    } else {
        echo "\n⚠️  无法获取微信服务器菜单（可能是IP白名单限制）\n";
        echo "建议：\n";
        echo "1. 在微信公众平台添加服务器IP到白名单\n";
        echo "2. 或者通过浏览器访问: https://你的域名/Prime/Service/checkmenu?id={$service_id}\n";
        echo "3. 或者直接重新推送菜单: https://你的域名/Prime/Service/taskmenu?id={$service_id}\n";
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}

/**
 * 获取微信Access Token
 */
function getAccessToken($appid, $secret) {
    $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";
    
    $response = httpGet($url);
    if (!$response) {
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (isset($data['access_token'])) {
        return $data['access_token'];
    }
    
    echo "❌ 获取Access Token失败：" . ($data['errmsg'] ?? '未知错误') . "\n";
    return false;
}

/**
 * 获取微信菜单
 */
function getWechatMenu($access_token) {
    $url = "https://api.weixin.qq.com/cgi-bin/menu/get?access_token={$access_token}";
    
    $response = httpGet($url);
    if (!$response) {
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (isset($data['menu'])) {
        return $data['menu'];
    }
    
    if (isset($data['errcode']) && $data['errcode'] == 46003) {
        echo "⚠️ 菜单不存在（可能已被删除）\n";
        return null;
    }
    
    echo "❌ 获取菜单失败：" . ($data['errmsg'] ?? '未知错误') . "\n";
    return false;
}

/**
 * HTTP GET请求
 */
function httpGet($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "CURL错误：" . curl_error($ch) . "\n";
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    if ($httpCode != 200) {
        echo "HTTP请求失败，状态码：{$httpCode}\n";
        return false;
    }
    
    return $response;
}

/**
 * 提取菜单中的所有URL
 */
function extractUrls($menu) {
    $urls = [];
    
    if (isset($menu['button'])) {
        foreach ($menu['button'] as $button) {
            if (isset($button['url'])) {
                $urls[$button['name']] = $button['url'];
            }
            if (isset($button['sub_button'])) {
                foreach ($button['sub_button'] as $sub) {
                    if (isset($sub['url'])) {
                        $urls[$sub['name']] = $sub['url'];
                    }
                }
            }
        }
    }
    
    return $urls;
}

echo "\n=== 使用说明 ===\n";
echo "1. 这个工具会显示数据库中的菜单配置\n";
echo "2. 如果能连接微信API，会对比两边的配置\n";
echo "3. 如果IP白名单限制，建议通过浏览器访问后台管理功能\n";
echo "4. 查询其他服务号: php simple_check_menu.php (然后访问 ?id=2)\n";
?>
