<?php
/**
 * 微信服务号菜单查询工具
 * 用于检查当前微信服务器上的菜单配置
 */

// 数据库配置 - 请根据你的实际配置修改
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'zcgk',
    'charset' => 'utf8'
];

// 微信服务号ID
$service_id = isset($_GET['id']) ? intval($_GET['id']) : 1;

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}", 
        $db_config['username'], 
        $db_config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取服务号配置
    $stmt = $pdo->prepare("SELECT name, appid, secret, menu_content FROM z_service WHERE id = ?");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$service) {
        throw new Exception("服务号ID {$service_id} 不存在");
    }
    
    echo "<h2>微信服务号菜单查询工具</h2>";
    echo "<h3>服务号：{$service['name']}</h3>";
    echo "<p>AppID: {$service['appid']}</p>";
    
    // 获取Access Token
    $access_token = getAccessToken($service['appid'], $service['secret']);
    
    if (!$access_token) {
        throw new Exception("获取Access Token失败");
    }
    
    echo "<p>✅ Access Token获取成功</p>";
    
    // 查询微信服务器上的菜单
    $wechat_menu = getWechatMenu($access_token);
    
    // 显示数据库中的菜单配置
    echo "<h4>📋 数据库中的菜单配置：</h4>";
    $db_menu = json_decode($service['menu_content'], true);
    echo "<pre>" . json_encode($db_menu, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
    
    // 显示微信服务器上的菜单配置
    echo "<h4>🔍 微信服务器上的菜单配置：</h4>";
    if ($wechat_menu) {
        echo "<pre>" . json_encode($wechat_menu, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
        
        // 比较两个菜单配置
        compareMenus($db_menu, $wechat_menu);
    } else {
        echo "<p style='color: red;'>❌ 微信服务器上没有菜单或获取失败</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误：" . $e->getMessage() . "</p>";
}

/**
 * 获取微信Access Token
 */
function getAccessToken($appid, $secret) {
    $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";
    
    $response = httpGet($url);
    $data = json_decode($response, true);
    
    if (isset($data['access_token'])) {
        return $data['access_token'];
    }
    
    echo "<p style='color: red;'>获取Access Token失败：" . ($data['errmsg'] ?? '未知错误') . "</p>";
    return false;
}

/**
 * 获取微信菜单
 */
function getWechatMenu($access_token) {
    $url = "https://api.weixin.qq.com/cgi-bin/menu/get?access_token={$access_token}";
    
    $response = httpGet($url);
    $data = json_decode($response, true);
    
    if (isset($data['menu'])) {
        return $data['menu'];
    }
    
    if (isset($data['errcode']) && $data['errcode'] == 46003) {
        echo "<p style='color: orange;'>⚠️ 菜单不存在（可能已被删除）</p>";
        return null;
    }
    
    echo "<p style='color: red;'>获取菜单失败：" . ($data['errmsg'] ?? '未知错误') . "</p>";
    return false;
}

/**
 * HTTP GET请求
 */
function httpGet($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('CURL错误：' . curl_error($ch));
    }
    
    curl_close($ch);
    
    if ($httpCode != 200) {
        throw new Exception("HTTP请求失败，状态码：{$httpCode}");
    }
    
    return $response;
}

/**
 * 比较数据库菜单和微信服务器菜单
 */
function compareMenus($db_menu, $wechat_menu) {
    echo "<h4>🔄 菜单对比结果：</h4>";
    
    // 提取URL进行比较
    $db_urls = extractUrls($db_menu);
    $wechat_urls = extractUrls($wechat_menu);
    
    echo "<p><strong>数据库中的URL：</strong></p>";
    foreach ($db_urls as $name => $url) {
        echo "<p>• {$name}: <code>{$url}</code></p>";
    }
    
    echo "<p><strong>微信服务器上的URL：</strong></p>";
    foreach ($wechat_urls as $name => $url) {
        $color = (strpos($url, 'zhongcaiguoke.cn') !== false) ? 'green' : 'red';
        echo "<p>• {$name}: <code style='color: {$color};'>{$url}</code></p>";
    }
    
    // 检查是否一致
    $is_same = (json_encode($db_urls) === json_encode($wechat_urls));
    if ($is_same) {
        echo "<p style='color: green;'>✅ 数据库和微信服务器菜单一致</p>";
    } else {
        echo "<p style='color: red;'>❌ 数据库和微信服务器菜单不一致，需要重新推送菜单</p>";
        echo "<p><a href='/Prime/Service/taskmenu?id={$GLOBALS['service_id']}' target='_blank'>🚀 点击重新推送菜单</a></p>";
    }
}

/**
 * 提取菜单中的所有URL
 */
function extractUrls($menu) {
    $urls = [];
    
    if (isset($menu['button'])) {
        foreach ($menu['button'] as $button) {
            if (isset($button['url'])) {
                $urls[$button['name']] = $button['url'];
            }
            if (isset($button['sub_button'])) {
                foreach ($button['sub_button'] as $sub) {
                    if (isset($sub['url'])) {
                        $urls[$sub['name']] = $sub['url'];
                    }
                }
            }
        }
    }
    
    return $urls;
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>微信菜单查询工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 5px; border-radius: 3px; }
        h2, h3, h4 { color: #333; }
        p { margin: 10px 0; }
        a { color: #007cba; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div style="margin-top: 30px;">
        <h4>🛠️ 使用说明：</h4>
        <p>1. 这个工具会对比数据库中的菜单配置和微信服务器上的实际菜单</p>
        <p>2. 如果发现不一致，说明需要重新推送菜单</p>
        <p>3. 可以通过URL参数 <code>?id=服务号ID</code> 来查询不同的服务号</p>
        <p>4. 例如：<code>check_wechat_menu.php?id=2</code></p>
    </div>
</body>
</html>
